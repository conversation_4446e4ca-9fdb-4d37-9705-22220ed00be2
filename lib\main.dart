import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:crypto/crypto.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ESP32 BLE Mesh Provisioner',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const BLEMeshProvisionerPage(),
    );
  }
}

class BLEMeshProvisionerPage extends StatefulWidget {
  const BLEMeshProvisionerPage({super.key});

  @override
  State<BLEMeshProvisionerPage> createState() => _BLEMeshProvisionerPageState();
}

class _BLEMeshProvisionerPageState extends State<BLEMeshProvisionerPage> {
  // BLE Mesh Provisioning Service and Characteristics UUIDs
  static const String MESH_PROV_SERVICE_UUID =
      "00001827-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROV_DATA_IN_UUID =
      "00002adb-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROV_DATA_OUT_UUID =
      "00002adc-0000-1000-8000-00805f9b34fb";

  // BLE Mesh Proxy Service and Characteristics UUIDs
  static const String MESH_PROXY_SERVICE_UUID =
      "00001828-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROXY_DATA_IN_UUID =
      "00002add-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROXY_DATA_OUT_UUID =
      "00002ade-0000-1000-8000-00805f9b34fb";

  List<BluetoothDevice> _discoveredDevices = [];
  BluetoothDevice? _connectedDevice;
  bool _isScanning = false;
  bool _isConnected = false;
  bool _isProvisioning = false;
  String _statusMessage = "Ready to scan for devices";

  // Debug logging
  final List<String> _debugLogs = [];

  // Provisioning data
  final List<int> _appKey = List.generate(16, (index) => Random().nextInt(256));
  final List<int> _netKey = List.generate(16, (index) => Random().nextInt(256));
  final int _netKeyIndex = 0;
  final int _appKeyIndex = 0;
  final int _unicastAddress = 0x0001;

  // Provisioning characteristics
  BluetoothCharacteristic? _provDataInChar;
  BluetoothCharacteristic? _provDataOutChar;

  StreamSubscription<List<int>>? _dataOutSubscription;

  void addLog(String log) {
    _debugLogs.add('[${DateTime.now().toIso8601String()}] $log');
    if (_debugLogs.length > 200) {
      _debugLogs.removeAt(0); // Limit log size
    }
  }

  List<String> getLogs() => List.unmodifiable(_debugLogs);

  @override
  void initState() {
    super.initState();
    addLog('App initialized');
    _initBLE();
  }

  @override
  void dispose() {
    _dataOutSubscription?.cancel();
    super.dispose();
  }

  Future<void> _initBLE() async {
    addLog('Initializing BLE');
    // Request permissions
    await _requestPermissions();

    // Check if Bluetooth is available
    if (await FlutterBluePlus.isAvailable == false) {
      addLog('Bluetooth not available');
      setState(() {
        _statusMessage = "Bluetooth not available";
      });
      return;
    }

    // Turn on Bluetooth if it's off
    addLog('Turning on Bluetooth');
    await FlutterBluePlus.turnOn();
    addLog('BLE initialization completed');
  }

  Future<void> _requestPermissions() async {
    addLog('Requesting permissions');
    await [
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.location,
    ].request();
    addLog('Permissions requested');
  }

  Future<void> _startScan() async {
    if (_isScanning) return;

    addLog('Starting BLE scan');
    setState(() {
      _isScanning = true;
      _discoveredDevices.clear();
      _statusMessage = "Scanning for BLE Mesh devices...";
    });

    try {
      // Start scanning for devices with mesh provisioning service
      addLog('Scanning for devices with mesh provisioning service');
      FlutterBluePlus.startScan(
        withServices: [Guid(MESH_PROV_SERVICE_UUID)],
        timeout: const Duration(seconds: 10),
      );

      // Listen for scan results
      FlutterBluePlus.scanResults.listen((results) {
        for (ScanResult result in results) {
          if (!_discoveredDevices.contains(result.device)) {
            addLog('Found device: ${result.device.name.isEmpty ? 'Unknown' : result.device.name} (${result.device.id})');
            setState(() {
              _discoveredDevices.add(result.device);
            });
          }
        }
      });

      // Stop scanning after timeout
      await Future.delayed(const Duration(seconds: 3));
      await FlutterBluePlus.stopScan();
      addLog('Scan completed');
    } catch (e) {
      addLog('Error during scan: $e');
      setState(() {
        _statusMessage = "Error during scan: $e";
      });
    }

    setState(() {
      _isScanning = false;
      if (_discoveredDevices.isEmpty) {
        addLog('No BLE Mesh devices found');
        _statusMessage = "No BLE Mesh devices found";
      } else {
        addLog('Found ${_discoveredDevices.length} device(s)');
        _statusMessage = "Found ${_discoveredDevices.length} device(s)";
      }
    });
  }

  Future<void> _connectToDevice(BluetoothDevice device) async {
    if (_isConnected) return;

    addLog('Attempting to connect to device: ${device.name.isEmpty ? 'Unknown' : device.name} (${device.id})');
    setState(() {
      _statusMessage = "Connecting to ${device.name}...";
    });

    try {
      await device.connect(timeout: const Duration(seconds: 10));
      addLog('Successfully connected to device');

      setState(() {
        _connectedDevice = device;
        _isConnected = true;
        _statusMessage = "Connected to ${device.name}";
      });

      // Discover services
      await _discoverServices(device);
    } catch (e) {
      addLog('Failed to connect: $e');
      setState(() {
        _statusMessage = "Failed to connect: $e";
      });
    }
  }

  Future<void> _discoverServices(BluetoothDevice device) async {
    try {
      addLog('Discovering services');
      List<BluetoothService> services = await device.discoverServices();
      addLog('Found ${services.length} services');

      for (BluetoothService service in services) {
        addLog('Service UUID: ${service.uuid.toString()}');
        if (service.uuid.toString().toLowerCase() ==
                MESH_PROV_SERVICE_UUID.toLowerCase() ||
            service.uuid.toString().toLowerCase() == "1827") {
          addLog('Found Mesh Provisioning Service');
          setState(() {
            _statusMessage = "Found Mesh Provisioning Service";
          });

          // Find characteristics
          for (BluetoothCharacteristic characteristic
              in service.characteristics) {
            String charUuid = characteristic.uuid.toString().toLowerCase();
            addLog('Characteristic UUID: $charUuid');

            if (charUuid == MESH_PROV_DATA_IN_UUID.toLowerCase() ||
                charUuid == "2adb") {
              _provDataInChar = characteristic;
              addLog('Found Provisioning Data In characteristic');
            } else if (charUuid == MESH_PROV_DATA_OUT_UUID.toLowerCase() ||
                charUuid == "2adc") {
              _provDataOutChar = characteristic;
              addLog('Found Provisioning Data Out characteristic');
              // Subscribe to notifications
              await characteristic.setNotifyValue(true);
              addLog('Subscribed to notifications');
              _dataOutSubscription =
                  characteristic.value.listen(_onProvisioningDataReceived);
            }
          }
          break;
        }
      }

      if (_provDataInChar != null && _provDataOutChar != null) {
        addLog('All provisioning characteristics found - ready to provision');
        setState(() {
          _statusMessage = "Ready to provision device";
        });
      } else {
        addLog('Provisioning characteristics not found');
        setState(() {
          _statusMessage = "Provisioning characteristics not found";
        });
      }
    } catch (e) {
      addLog('Error discovering services: $e');
      setState(() {
        _statusMessage = "Error discovering services: $e";
      });
    }
  }

  Future<void> _startProvisioning() async {
    if (_isProvisioning ||
        _provDataInChar == null ||
        _provDataOutChar == null) {
      addLog('Cannot start provisioning - already in progress or characteristics not available');
      return;
    }

    addLog('Starting provisioning process');
    setState(() {
      _isProvisioning = true;
      _statusMessage = "Starting provisioning process...";
    });

    try {
      // Step 1: Send Link Open (PB-GATT Bearer)
      await _sendLinkOpen();

      // Wait a bit for link establishment
      await Future.delayed(const Duration(milliseconds: 500));

      // Step 2: Send Provisioning Invite
      await _sendProvisioningInvite();

      // Wait for response and continue with provisioning steps
      await Future.delayed(const Duration(seconds: 3));

      // Check if we received any response
      addLog('Waiting for device response...');
      setState(() {
        _statusMessage = "Waiting for device response...";
      });

      // Wait a bit more for potential delayed responses
      await Future.delayed(const Duration(seconds: 2));

      addLog('Provisioning initiated - waiting for device capabilities');
      setState(() {
        _statusMessage = "Provisioning initiated. Waiting for device response.";
      });
    } catch (e) {
      addLog('Provisioning failed: $e');
      setState(() {
        _statusMessage = "Provisioning failed: $e";
      });
    }

    setState(() {
      _isProvisioning = false;
    });
  }

  Future<void> _sendLinkOpen() async {
    if (_provDataInChar == null) return;

    // Some ESP32 implementations require Link Open before provisioning
    // Link Open PDU: [0x00] + Device UUID (16 bytes)
    // For simplicity, we'll use a generic UUID or skip if not needed
    addLog('Attempting to send Link Open (if required by device)');

    // Try sending a simple link establishment
    List<int> linkOpenPdu = [0x00]; // Link Open opcode

    try {
      await _provDataInChar!.write(linkOpenPdu, withoutResponse: true);
      addLog('Link Open sent');
    } catch (e) {
      addLog('Link Open failed (may not be required): $e');
    }
  }

  Future<void> _sendLinkOpen() async {
    if (_provDataInChar == null) return;

    // Some ESP32 implementations require Link Open before provisioning
    // Link Open PDU: [0x00] + Device UUID (16 bytes)
    // For simplicity, we'll use a generic UUID or skip if not needed
    addLog('Attempting to send Link Open (if required by device)');

    // Try sending a simple link establishment
    List<int> linkOpenPdu = [0x00]; // Link Open opcode

    try {
      await _provDataInChar!.write(linkOpenPdu, withoutResponse: true);
      addLog('Link Open sent');
    } catch (e) {
      addLog('Link Open failed (may not be required): $e');
    }
  }

  Future<void> _sendProvisioningInvite() async {
    if (_provDataInChar == null) return;

    // Provisioning Invite PDU
    // Format: [Type (1 byte)] [AttentionDuration (1 byte)]
    List<int> invitePdu = [
      0x00, // Type: Invite
      0x00  // AttentionDuration: 0 seconds (no blinking)
    ];

    addLog('Sending provisioning invite PDU: ${invitePdu.map((e) => '0x${e.toRadixString(16).padLeft(2, '0')}').join(' ')}');

    // Try both write methods to ensure compatibility
    try {
      await _provDataInChar!.write(invitePdu, withoutResponse: false);
      addLog('Provisioning invite sent with response');
    } catch (e) {
      addLog('Write with response failed, trying without response: $e');
      await _provDataInChar!.write(invitePdu, withoutResponse: true);
      addLog('Provisioning invite sent without response');
    }

    addLog('Provisioning invite sent successfully');
    setState(() {
      _statusMessage = "Sent provisioning invite";
    });
  }

  void _onProvisioningDataReceived(List<int> data) {
    // Handle incoming provisioning data
    addLog('Received provisioning data: ${data.map((e) => '0x${e.toRadixString(16).padLeft(2, '0')}').join(' ')}');
    if (data.isNotEmpty) {
      int pduType = data[0];
      addLog('PDU Type: 0x${pduType.toRadixString(16).padLeft(2, '0')}');

      switch (pduType) {
        case 0x01: // Provisioning Capabilities
          _handleProvisioningCapabilities(data);
          break;
        case 0x02: // Provisioning Start
          _handleProvisioningStart(data);
          break;
        case 0x03: // Provisioning Public Key
          _handleProvisioningPublicKey(data);
          break;
        case 0x04: // Provisioning Input Complete
          _handleProvisioningInputComplete(data);
          break;
        case 0x05: // Provisioning Confirmation
          _handleProvisioningConfirmation(data);
          break;
        case 0x06: // Provisioning Random
          _handleProvisioningRandom(data);
          break;
        case 0x07: // Provisioning Data
          _handleProvisioningData(data);
          break;
        case 0x08: // Provisioning Complete
          _handleProvisioningComplete(data);
          break;
        case 0x09: // Provisioning Failed
          _handleProvisioningFailed(data);
          break;
        default:
          addLog('Received unknown PDU type: $pduType');
          setState(() {
            _statusMessage = "Received unknown PDU type: $pduType";
          });
      }
    }
  }

  void _handleProvisioningCapabilities(List<int> data) {
    addLog('Handling provisioning capabilities');
    setState(() {
      _statusMessage = "Received device capabilities";
    });

    // Send Provisioning Start
    _sendProvisioningStart();
  }

  Future<void> _sendProvisioningStart() async {
    if (_provDataInChar == null) return;

    // Provisioning Start PDU
    // Format: [Type (1 byte)] [Algorithm (1 byte)] [Public Key (1 byte)] [Auth Method (1 byte)] [Auth Action (1 byte)] [Auth Size (1 byte)]
    List<int> startPdu = [
      0x02, // Type: Start
      0x00, // Algorithm: P-256 Elliptic Curve
      0x00, // Public Key: No OOB Public Key
      0x00, // Auth Method: No OOB
      0x00, // Auth Action: No Action
      0x00, // Auth Size: 0
    ];

    addLog('Sending provisioning start PDU: ${startPdu.map((e) => '0x${e.toRadixString(16).padLeft(2, '0')}').join(' ')}');
    await _provDataInChar!.write(startPdu, withoutResponse: true);

    addLog('Provisioning start sent successfully');
    setState(() {
      _statusMessage = "Sent provisioning start";
    });
  }

  void _handleProvisioningStart(List<int> data) {
    // Handle provisioning start response
    addLog('Handling provisioning start response');
    setState(() {
      _statusMessage = "Provisioning start acknowledged";
    });
  }

  void _handleProvisioningPublicKey(List<int> data) {
    addLog('Handling provisioning public key');
    setState(() {
      _statusMessage = "Received device public key";
    });

    // Send our public key
    _sendProvisioningPublicKey();
  }

  Future<void> _sendProvisioningPublicKey() async {
    if (_provDataInChar == null) return;

    // Generate a dummy public key (64 bytes for P-256)
    // In a real implementation, this would be a proper ECDH key pair
    List<int> publicKey = List.generate(64, (index) => Random().nextInt(256));

    List<int> publicKeyPdu = [0x03] + publicKey; // Type: Public Key + Key data

    addLog('Sending provisioning public key (${publicKey.length} bytes)');
    await _provDataInChar!.write(publicKeyPdu, withoutResponse: true);

    addLog('Provisioning public key sent successfully');
    setState(() {
      _statusMessage = "Sent provisioning public key";
    });
  }

  void _handleProvisioningInputComplete(List<int> data) {
    addLog('Handling provisioning input complete');
    setState(() {
      _statusMessage = "Input complete received";
    });
  }

  void _handleProvisioningConfirmation(List<int> data) {
    addLog('Handling provisioning confirmation');
    setState(() {
      _statusMessage = "Received confirmation";
    });

    // Send our confirmation
    _sendProvisioningConfirmation();
  }

  Future<void> _sendProvisioningConfirmation() async {
    if (_provDataInChar == null) return;

    // Generate confirmation value (16 bytes)
    List<int> confirmation =
        List.generate(16, (index) => Random().nextInt(256));

    List<int> confirmationPdu =
        [0x05] + confirmation; // Type: Confirmation + Confirmation data

    addLog('Sending provisioning confirmation (${confirmation.length} bytes)');
    await _provDataInChar!.write(confirmationPdu, withoutResponse: true);

    addLog('Provisioning confirmation sent successfully');
    setState(() {
      _statusMessage = "Sent provisioning confirmation";
    });
  }

  void _handleProvisioningRandom(List<int> data) {
    addLog('Handling provisioning random');
    setState(() {
      _statusMessage = "Received random value";
    });

    // Send our random value
    _sendProvisioningRandom();
  }

  Future<void> _sendProvisioningRandom() async {
    if (_provDataInChar == null) return;

    // Generate random value (16 bytes)
    List<int> random = List.generate(16, (index) => Random().nextInt(256));

    List<int> randomPdu = [0x06] + random; // Type: Random + Random data

    addLog('Sending provisioning random (${random.length} bytes)');
    await _provDataInChar!.write(randomPdu, withoutResponse: true);

    addLog('Provisioning random sent successfully');
    setState(() {
      _statusMessage = "Sent provisioning random";
    });

    // After random exchange, send provisioning data
    await Future.delayed(const Duration(milliseconds: 100));
    _sendProvisioningData();
  }

  Future<void> _sendProvisioningData() async {
    if (_provDataInChar == null) return;

    addLog('Preparing provisioning data');
    // Create provisioning data
    List<int> provisioningData = [];

    // Network Key (16 bytes)
    provisioningData.addAll(_netKey);

    // Key Index (2 bytes)
    provisioningData.addAll([_netKeyIndex & 0xFF, (_netKeyIndex >> 8) & 0xFF]);

    // Flags (1 byte) - Key refresh and IV update flags
    provisioningData.add(0x00);

    // IV Index (4 bytes)
    provisioningData.addAll([0x00, 0x00, 0x00, 0x00]);

    // Unicast Address (2 bytes)
    provisioningData
        .addAll([_unicastAddress & 0xFF, (_unicastAddress >> 8) & 0xFF]);

    addLog('Provisioning data prepared (${provisioningData.length} bytes)');
    // Encrypt the provisioning data (simplified - should use proper AES-CCM encryption)
    List<int> encryptedData = _encryptProvisioningData(provisioningData);

    List<int> dataPdu = [0x07] + encryptedData; // Type: Data + Encrypted data

    addLog('Sending provisioning data PDU');
    await _provDataInChar!.write(dataPdu, withoutResponse: true);

    addLog('Provisioning data sent successfully');
    setState(() {
      _statusMessage = "Sent provisioning data";
    });
  }

  List<int> _encryptProvisioningData(List<int> data) {
    // This is a simplified encryption - in a real implementation,
    // you would use proper AES-CCM encryption with the session key
    addLog('Encrypting provisioning data (simplified)');
    return data;
  }

  void _handleProvisioningData(List<int> data) {
    addLog('Handling provisioning data acknowledgment');
    setState(() {
      _statusMessage = "Provisioning data acknowledged";
    });
  }

  void _handleProvisioningComplete(List<int> data) {
    addLog('Provisioning completed successfully!');
    setState(() {
      _statusMessage = "Provisioning completed successfully!";
    });

    // Provisioning is complete, now we can send app key
    _sendAppKey();
  }

  Future<void> _sendAppKey() async {
    // After provisioning, we need to send the application key
    // This would typically be done through the mesh proxy service

    addLog('Sending application key');
    setState(() {
      _statusMessage = "Sending application key...";
    });

    // This is a simplified version - in a real implementation,
    // you would need to establish a secure channel and send the app key
    // through the mesh network layer

    await Future.delayed(const Duration(seconds: 1));

    addLog('Device provisioned and configured successfully!');
    setState(() {
      _statusMessage = "Device provisioned and configured successfully!";
    });
  }

  void _handleProvisioningFailed(List<int> data) {
    int errorCode = data.length > 1 ? data[1] : 0;
    addLog('Provisioning failed with error code: $errorCode');
    setState(() {
      _statusMessage = "Provisioning failed with error code: $errorCode";
    });
  }

  Future<void> _disconnect() async {
    if (_connectedDevice != null) {
      addLog('Disconnecting from device: ${_connectedDevice!.name.isEmpty ? 'Unknown' : _connectedDevice!.name}');
      await _connectedDevice!.disconnect();
      addLog('Device disconnected successfully');
      setState(() {
        _connectedDevice = null;
        _isConnected = false;
        _statusMessage = "Disconnected";
      });
    }
  }

  void _showLogsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Debug Logs'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Total logs: ${_debugLogs.length}'),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _debugLogs.clear();
                        });
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Clear Logs'),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: ListView.builder(
                    itemCount: _debugLogs.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2.0),
                        child: Text(
                          _debugLogs[index],
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ESP32 BLE Mesh Provisioner'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(_statusMessage),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isScanning ? null : _startScan,
                    child: _isScanning
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 16,
                                height: 16,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              ),
                              SizedBox(width: 8),
                              Text('Scanning...'),
                            ],
                          )
                        : const Text('Scan for Devices'),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isConnected ? _disconnect : null,
                  child: const Text('Disconnect'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => _showLogsDialog(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('View Logs'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'Discovered Devices',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _discoveredDevices.length,
                        itemBuilder: (context, index) {
                          final device = _discoveredDevices[index];
                          final isConnected = _connectedDevice == device;

                          return ListTile(
                            title: Text(device.name.isEmpty
                                ? 'Unknown Device'
                                : device.name),
                            subtitle: Text(device.id.toString()),
                            trailing: isConnected
                                ? const Icon(Icons.bluetooth_connected,
                                    color: Colors.green)
                                : ElevatedButton(
                                    onPressed: () => _connectToDevice(device),
                                    child: const Text('Connect'),
                                  ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed:
                  _isConnected && !_isProvisioning ? _startProvisioning : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: _isProvisioning
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('Provisioning...'),
                      ],
                    )
                  : const Text('Start Provisioning'),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Provisioning Info',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                        'Network Key: ${_netKey.map((e) => e.toRadixString(16).padLeft(2, '0')).join(':')}'),
                    Text(
                        'App Key: ${_appKey.map((e) => e.toRadixString(16).padLeft(2, '0')).join(':')}'),
                    Text(
                        'Unicast Address: 0x${_unicastAddress.toRadixString(16).padLeft(4, '0')}'),
                    Text('Net Key Index: $_netKeyIndex'),
                    Text('App Key Index: $_appKeyIndex'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
