import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:crypto/crypto.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ESP32 BLE Mesh Provisioner',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const BLEMeshProvisionerPage(),
    );
  }
}

class BLEMeshProvisionerPage extends StatefulWidget {
  const BLEMeshProvisionerPage({super.key});

  @override
  State<BLEMeshProvisionerPage> createState() => _BLEMeshProvisionerPageState();
}

class _BLEMeshProvisionerPageState extends State<BLEMeshProvisionerPage> {
  // BLE Mesh Provisioning Service and Characteristics UUIDs
  static const String MESH_PROV_SERVICE_UUID =
      "00001827-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROV_DATA_IN_UUID =
      "00002adb-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROV_DATA_OUT_UUID =
      "00002adc-0000-1000-8000-00805f9b34fb";

  // BLE Mesh Proxy Service and Characteristics UUIDs
  static const String MESH_PROXY_SERVICE_UUID =
      "00001828-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROXY_DATA_IN_UUID =
      "00002add-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROXY_DATA_OUT_UUID =
      "00002ade-0000-1000-8000-00805f9b34fb";

  List<BluetoothDevice> _discoveredDevices = [];
  BluetoothDevice? _connectedDevice;
  bool _isScanning = false;
  bool _isConnected = false;
  bool _isProvisioning = false;
  String _statusMessage = "Ready to scan for devices";

  // Provisioning data
  final List<int> _appKey = List.generate(16, (index) => Random().nextInt(256));
  final List<int> _netKey = List.generate(16, (index) => Random().nextInt(256));
  final int _netKeyIndex = 0;
  final int _appKeyIndex = 0;
  final int _unicastAddress = 0x0001;

  // Provisioning characteristics
  BluetoothCharacteristic? _provDataInChar;
  BluetoothCharacteristic? _provDataOutChar;

  StreamSubscription<List<int>>? _dataOutSubscription;

  @override
  void initState() {
    super.initState();
    _initBLE();
  }

  @override
  void dispose() {
    _dataOutSubscription?.cancel();
    super.dispose();
  }

  Future<void> _initBLE() async {
    // Request permissions
    await _requestPermissions();

    // Check if Bluetooth is available
    if (await FlutterBluePlus.isAvailable == false) {
      setState(() {
        _statusMessage = "Bluetooth not available";
      });
      return;
    }

    // Turn on Bluetooth if it's off
    await FlutterBluePlus.turnOn();
  }

  Future<void> _requestPermissions() async {
    await [
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.location,
    ].request();
  }

  Future<void> _startScan() async {
    if (_isScanning) return;

    setState(() {
      _isScanning = true;
      _discoveredDevices.clear();
      _statusMessage = "Scanning for BLE Mesh devices...";
    });

    try {
      // Start scanning for devices with mesh provisioning service
      FlutterBluePlus.startScan(
        withServices: [Guid(MESH_PROV_SERVICE_UUID)],
        timeout: const Duration(seconds: 10),
      );

      // Listen for scan results
      FlutterBluePlus.scanResults.listen((results) {
        for (ScanResult result in results) {
          if (!_discoveredDevices.contains(result.device)) {
            setState(() {
              _discoveredDevices.add(result.device);
            });
          }
        }
      });

      // Stop scanning after timeout
      await Future.delayed(const Duration(seconds: 10));
      await FlutterBluePlus.stopScan();
    } catch (e) {
      setState(() {
        _statusMessage = "Error during scan: $e";
      });
    }

    setState(() {
      _isScanning = false;
      if (_discoveredDevices.isEmpty) {
        _statusMessage = "No BLE Mesh devices found";
      } else {
        _statusMessage = "Found ${_discoveredDevices.length} device(s)";
      }
    });
  }

  Future<void> _connectToDevice(BluetoothDevice device) async {
    if (_isConnected) return;

    setState(() {
      _statusMessage = "Connecting to ${device.name}...";
    });

    try {
      await device.connect(timeout: const Duration(seconds: 10));

      setState(() {
        _connectedDevice = device;
        _isConnected = true;
        _statusMessage = "Connected to ${device.name}";
      });

      // Discover services
      await _discoverServices(device);
    } catch (e) {
      setState(() {
        _statusMessage = "Failed to connect: $e";
      });
    }
  }

  Future<void> _discoverServices(BluetoothDevice device) async {
    try {
      List<BluetoothService> services = await device.discoverServices();

      for (BluetoothService service in services) {
        if (service.uuid.toString().toLowerCase() ==
                MESH_PROV_SERVICE_UUID.toLowerCase() ||
            service.uuid.toString().toLowerCase() == "1827") {
          setState(() {
            _statusMessage = "Found Mesh Provisioning Service";
          });

          // Find characteristics
          for (BluetoothCharacteristic characteristic
              in service.characteristics) {
            String charUuid = characteristic.uuid.toString().toLowerCase();

            if (charUuid == MESH_PROV_DATA_IN_UUID.toLowerCase() ||
                charUuid == "2adb") {
              _provDataInChar = characteristic;
            } else if (charUuid == MESH_PROV_DATA_OUT_UUID.toLowerCase() ||
                charUuid == "2adc") {
              _provDataOutChar = characteristic;
              // Subscribe to notifications
              await characteristic.setNotifyValue(true);
              _dataOutSubscription =
                  characteristic.value.listen(_onProvisioningDataReceived);
            }
          }
          break;
        }
      }

      if (_provDataInChar != null && _provDataOutChar != null) {
        setState(() {
          _statusMessage = "Ready to provision device";
        });
      } else {
        setState(() {
          _statusMessage = "Provisioning characteristics not found";
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = "Error discovering services: $e";
      });
    }
  }

  Future<void> _startProvisioning() async {
    if (_isProvisioning ||
        _provDataInChar == null ||
        _provDataOutChar == null) {
      return;
    }

    setState(() {
      _isProvisioning = true;
      _statusMessage = "Starting provisioning process...";
    });

    try {
      // Step 1: Send Provisioning Invite
      await _sendProvisioningInvite();

      // Wait for response and continue with provisioning steps
      await Future.delayed(const Duration(seconds: 1));

      // Step 2: Send Provisioning Capabilities
      // This would normally be part of a state machine handling the full provisioning protocol

      setState(() {
        _statusMessage = "Provisioning initiated. Check device for completion.";
      });
    } catch (e) {
      setState(() {
        _statusMessage = "Provisioning failed: $e";
      });
    }

    setState(() {
      _isProvisioning = false;
    });
  }

  Future<void> _sendProvisioningInvite() async {
    if (_provDataInChar == null) return;

    // Provisioning Invite PDU
    // Format: [Type (1 byte)] [AttentionDuration (1 byte)]
    List<int> invitePdu = [
      0x00,
      0x00
    ]; // Type: Invite, AttentionDuration: 0 seconds

    await _provDataInChar!.write(invitePdu, withoutResponse: false);

    setState(() {
      _statusMessage = "Sent provisioning invite";
    });
  }

  void _onProvisioningDataReceived(List<int> data) {
    // Handle incoming provisioning data
    if (data.isNotEmpty) {
      int pduType = data[0];

      switch (pduType) {
        case 0x01: // Provisioning Capabilities
          _handleProvisioningCapabilities(data);
          break;
        case 0x02: // Provisioning Start
          _handleProvisioningStart(data);
          break;
        case 0x03: // Provisioning Public Key
          _handleProvisioningPublicKey(data);
          break;
        case 0x04: // Provisioning Input Complete
          _handleProvisioningInputComplete(data);
          break;
        case 0x05: // Provisioning Confirmation
          _handleProvisioningConfirmation(data);
          break;
        case 0x06: // Provisioning Random
          _handleProvisioningRandom(data);
          break;
        case 0x07: // Provisioning Data
          _handleProvisioningData(data);
          break;
        case 0x08: // Provisioning Complete
          _handleProvisioningComplete(data);
          break;
        case 0x09: // Provisioning Failed
          _handleProvisioningFailed(data);
          break;
        default:
          setState(() {
            _statusMessage = "Received unknown PDU type: $pduType";
          });
      }
    }
  }

  void _handleProvisioningCapabilities(List<int> data) {
    setState(() {
      _statusMessage = "Received device capabilities";
    });

    // Send Provisioning Start
    _sendProvisioningStart();
  }

  Future<void> _sendProvisioningStart() async {
    if (_provDataInChar == null) return;

    // Provisioning Start PDU
    // Format: [Type (1 byte)] [Algorithm (1 byte)] [Public Key (1 byte)] [Auth Method (1 byte)] [Auth Action (1 byte)] [Auth Size (1 byte)]
    List<int> startPdu = [
      0x02, // Type: Start
      0x00, // Algorithm: P-256 Elliptic Curve
      0x00, // Public Key: No OOB Public Key
      0x00, // Auth Method: No OOB
      0x00, // Auth Action: No Action
      0x00, // Auth Size: 0
    ];

    await _provDataInChar!.write(startPdu, withoutResponse: false);

    setState(() {
      _statusMessage = "Sent provisioning start";
    });
  }

  void _handleProvisioningStart(List<int> data) {
    // Handle provisioning start response
    setState(() {
      _statusMessage = "Provisioning start acknowledged";
    });
  }

  void _handleProvisioningPublicKey(List<int> data) {
    setState(() {
      _statusMessage = "Received device public key";
    });

    // Send our public key
    _sendProvisioningPublicKey();
  }

  Future<void> _sendProvisioningPublicKey() async {
    if (_provDataInChar == null) return;

    // Generate a dummy public key (64 bytes for P-256)
    // In a real implementation, this would be a proper ECDH key pair
    List<int> publicKey = List.generate(64, (index) => Random().nextInt(256));

    List<int> publicKeyPdu = [0x03] + publicKey; // Type: Public Key + Key data

    await _provDataInChar!.write(publicKeyPdu, withoutResponse: false);

    setState(() {
      _statusMessage = "Sent provisioning public key";
    });
  }

  void _handleProvisioningInputComplete(List<int> data) {
    setState(() {
      _statusMessage = "Input complete received";
    });
  }

  void _handleProvisioningConfirmation(List<int> data) {
    setState(() {
      _statusMessage = "Received confirmation";
    });

    // Send our confirmation
    _sendProvisioningConfirmation();
  }

  Future<void> _sendProvisioningConfirmation() async {
    if (_provDataInChar == null) return;

    // Generate confirmation value (16 bytes)
    List<int> confirmation =
        List.generate(16, (index) => Random().nextInt(256));

    List<int> confirmationPdu =
        [0x05] + confirmation; // Type: Confirmation + Confirmation data

    await _provDataInChar!.write(confirmationPdu, withoutResponse: false);

    setState(() {
      _statusMessage = "Sent provisioning confirmation";
    });
  }

  void _handleProvisioningRandom(List<int> data) {
    setState(() {
      _statusMessage = "Received random value";
    });

    // Send our random value
    _sendProvisioningRandom();
  }

  Future<void> _sendProvisioningRandom() async {
    if (_provDataInChar == null) return;

    // Generate random value (16 bytes)
    List<int> random = List.generate(16, (index) => Random().nextInt(256));

    List<int> randomPdu = [0x06] + random; // Type: Random + Random data

    await _provDataInChar!.write(randomPdu, withoutResponse: false);

    setState(() {
      _statusMessage = "Sent provisioning random";
    });

    // After random exchange, send provisioning data
    await Future.delayed(const Duration(milliseconds: 100));
    _sendProvisioningData();
  }

  Future<void> _sendProvisioningData() async {
    if (_provDataInChar == null) return;

    // Create provisioning data
    List<int> provisioningData = [];

    // Network Key (16 bytes)
    provisioningData.addAll(_netKey);

    // Key Index (2 bytes)
    provisioningData.addAll([_netKeyIndex & 0xFF, (_netKeyIndex >> 8) & 0xFF]);

    // Flags (1 byte) - Key refresh and IV update flags
    provisioningData.add(0x00);

    // IV Index (4 bytes)
    provisioningData.addAll([0x00, 0x00, 0x00, 0x00]);

    // Unicast Address (2 bytes)
    provisioningData
        .addAll([_unicastAddress & 0xFF, (_unicastAddress >> 8) & 0xFF]);

    // Encrypt the provisioning data (simplified - should use proper AES-CCM encryption)
    List<int> encryptedData = _encryptProvisioningData(provisioningData);

    List<int> dataPdu = [0x07] + encryptedData; // Type: Data + Encrypted data

    await _provDataInChar!.write(dataPdu, withoutResponse: false);

    setState(() {
      _statusMessage = "Sent provisioning data";
    });
  }

  List<int> _encryptProvisioningData(List<int> data) {
    // This is a simplified encryption - in a real implementation,
    // you would use proper AES-CCM encryption with the session key
    return data;
  }

  void _handleProvisioningData(List<int> data) {
    setState(() {
      _statusMessage = "Provisioning data acknowledged";
    });
  }

  void _handleProvisioningComplete(List<int> data) {
    setState(() {
      _statusMessage = "Provisioning completed successfully!";
    });

    // Provisioning is complete, now we can send app key
    _sendAppKey();
  }

  Future<void> _sendAppKey() async {
    // After provisioning, we need to send the application key
    // This would typically be done through the mesh proxy service

    setState(() {
      _statusMessage = "Sending application key...";
    });

    // This is a simplified version - in a real implementation,
    // you would need to establish a secure channel and send the app key
    // through the mesh network layer

    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _statusMessage = "Device provisioned and configured successfully!";
    });
  }

  void _handleProvisioningFailed(List<int> data) {
    int errorCode = data.length > 1 ? data[1] : 0;
    setState(() {
      _statusMessage = "Provisioning failed with error code: $errorCode";
    });
  }

  Future<void> _disconnect() async {
    if (_connectedDevice != null) {
      await _connectedDevice!.disconnect();
      setState(() {
        _connectedDevice = null;
        _isConnected = false;
        _statusMessage = "Disconnected";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ESP32 BLE Mesh Provisioner'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(_statusMessage),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isScanning ? null : _startScan,
                    child: _isScanning
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 16,
                                height: 16,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              ),
                              SizedBox(width: 8),
                              Text('Scanning...'),
                            ],
                          )
                        : const Text('Scan for Devices'),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isConnected ? _disconnect : null,
                  child: const Text('Disconnect'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'Discovered Devices',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _discoveredDevices.length,
                        itemBuilder: (context, index) {
                          final device = _discoveredDevices[index];
                          final isConnected = _connectedDevice == device;

                          return ListTile(
                            title: Text(device.name.isEmpty
                                ? 'Unknown Device'
                                : device.name),
                            subtitle: Text(device.id.toString()),
                            trailing: isConnected
                                ? const Icon(Icons.bluetooth_connected,
                                    color: Colors.green)
                                : ElevatedButton(
                                    onPressed: () => _connectToDevice(device),
                                    child: const Text('Connect'),
                                  ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed:
                  _isConnected && !_isProvisioning ? _startProvisioning : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: _isProvisioning
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('Provisioning...'),
                      ],
                    )
                  : const Text('Start Provisioning'),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Provisioning Info',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                        'Network Key: ${_netKey.map((e) => e.toRadixString(16).padLeft(2, '0')).join(':')}'),
                    Text(
                        'App Key: ${_appKey.map((e) => e.toRadixString(16).padLeft(2, '0')).join(':')}'),
                    Text(
                        'Unicast Address: 0x${_unicastAddress.toRadixString(16).padLeft(4, '0')}'),
                    Text('Net Key Index: $_netKeyIndex'),
                    Text('App Key Index: $_appKeyIndex'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
